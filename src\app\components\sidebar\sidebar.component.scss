.sidebar {
  width: 240px;
  height: 100vh;
  background-color: #1e293b;
  color: #fff;
  padding: 1rem;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;

  .logo {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
  }

  .nav-links {
    list-style: none;
    padding: 0;

    li {
      margin: 1rem 0;

      a {
        color: #cbd5e1;
        text-decoration: none;
        font-size: 1rem;
        display: block;
        padding: 0.5rem;
        border-radius: 8px;

        &:hover {
          background-color: #334155;
          color: #fff;
        }
      }
    }
  }
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    flex-direction: row;
    justify-content: space-around;

    .nav-links {
      display: flex;
      flex-direction: row;
      gap: 1rem;

      li {
        margin: 0;
      }
    }
  }
}